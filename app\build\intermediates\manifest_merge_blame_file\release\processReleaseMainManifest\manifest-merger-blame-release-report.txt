1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Zumbla.Burst2025"
4    android:versionCode="3"
5    android:versionName="2.1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:6:5-67
11-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:7:5-79
12-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:7:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:8:5-68
13-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:8:22-65
14    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
14-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:9:5-82
14-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:9:22-79
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:10:5-66
15-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:10:22-63
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:11:5-81
16-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:11:22-78
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:12:5-77
17-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:12:22-74
18    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
19    <permission
19-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
20        android:name="com.Zumbla.Burst2025.permission.C2D_MESSAGE"
20-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
21        android:protectionLevel="signature" />
21-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
22
23    <uses-permission android:name="com.Zumbla.Burst2025.permission.C2D_MESSAGE" /> <!-- START: ShortcutBadger -->
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
24    <!-- Samsung -->
25    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
25-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
25-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
26    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
27    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
28    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
29    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
29-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
29-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
30    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
30-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
30-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
31    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
31-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
31-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
32    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
32-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
32-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
33    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
34    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
35    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
35-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
35-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
36    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
36-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
36-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
37    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
37-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
37-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
38    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
38-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
38-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
39    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
40    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
40-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
40-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
41    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Android package visibility setting -->
41-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:26:5-79
41-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:26:22-76
42    <queries>
42-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:32:5-48:15
43
44        <!-- For browser content -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:35:9-41:18
46            <action android:name="android.intent.action.VIEW" />
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
47
48            <category android:name="android.intent.category.BROWSABLE" />
48-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:38:13-74
48-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:38:23-71
49
50            <data android:scheme="https" />
50-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:40:13-44
50-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:40:19-41
51        </intent>
52        <!-- End of browser content -->
53        <!-- For CustomTabsService -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:44:9-46:18
55            <action android:name="android.support.customtabs.action.CustomTabsService" />
55-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:45:13-90
55-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:45:21-87
56        </intent>
57        <!-- End of CustomTabsService -->
58    </queries>
59
60    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
60-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
60-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
61    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
61-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
61-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
62    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
62-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
62-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
63    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
63-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
63-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
64
65    <permission
65-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
66        android:name="com.Zumbla.Burst2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
66-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
67        android:protectionLevel="signature" />
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
68
69    <uses-permission android:name="com.Zumbla.Burst2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
70
71    <application
71-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:14:5-44:19
72        android:allowBackup="true"
72-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:15:9-35
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
74        android:extractNativeLibs="false"
75        android:hardwareAccelerated="true"
75-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:16:9-43
76        android:icon="@mipmap/ic_launcher"
76-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:17:9-43
77        android:label="@string/app_name"
77-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:18:9-41
78        android:supportsRtl="true"
78-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:19:9-35
79        android:theme="@style/Theme.Game"
79-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:21:9-42
80        android:usesCleartextTraffic="true" >
80-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:20:9-44
81        <meta-data
81-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:9-64
82            android:name="main_id"
82-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:20-42
83            android:value="91" />
83-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:43-61
84
85        <activity
85-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:24:9-32:20
86            android:name="com.Zumbla.Burst2025.MainActivity"
86-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:26:13-61
87            android:configChanges="orientation|screenSize"
87-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:27:13-59
88            android:exported="true" >
88-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:25:13-36
89            <intent-filter>
89-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:28:13-31:29
90                <action android:name="android.intent.action.MAIN" />
90-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:29:17-69
90-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:29:25-66
91
92                <category android:name="android.intent.category.LAUNCHER" />
92-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:30:17-77
92-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:30:27-74
93            </intent-filter>
94        </activity>
95        <activity android:name="com.Zumbla.Burst2025.PrivacyActivity" />
95-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:33:9-72
95-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:33:19-70
96
97        <meta-data
97-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:35:9-37:35
98            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
98-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:36:13-81
99            android:value="true" />
99-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:37:13-33
100        <meta-data
100-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:38:9-40:70
101            android:name="com.google.android.gms.ads.APPLICATION_ID"
101-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:39:13-69
102            android:value="ca-app-pub-7841751633097845~7145671857" />
102-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:40:13-67
103        <!-- OneSignal meta-data -->
104        <meta-data
104-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:9-102
105            android:name="com.onesignal.NotificationOpened.DEFAULT"
105-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:20-75
106            android:value="DISABLE" />
106-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:76-99
107        <meta-data
107-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:9-115
108            android:name="com.onesignal.NotificationWillShowInForeground.DEFAULT"
108-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:20-89
109            android:value="ENABLE" />
109-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:90-112
110
111        <receiver
111-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
112            android:name="com.onesignal.FCMBroadcastReceiver"
112-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
113            android:exported="true"
113-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
114            android:permission="com.google.android.c2dm.permission.SEND" >
114-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
115
116            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
117            <intent-filter android:priority="999" >
117-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
117-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
118                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
118-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
118-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
119
120                <category android:name="com.Zumbla.Burst2025" />
120-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
120-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
121            </intent-filter>
122        </receiver>
123
124        <service
124-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
125            android:name="com.onesignal.HmsMessageServiceOneSignal"
125-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
126            android:exported="false" >
126-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
127            <intent-filter>
127-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
128                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
128-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
128-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
129            </intent-filter>
130        </service>
131
132        <activity
132-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
133            android:name="com.onesignal.NotificationOpenedActivityHMS"
133-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
134            android:exported="true"
134-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
135            android:noHistory="true"
135-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
136-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
137            <intent-filter>
137-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
138                <action android:name="android.intent.action.VIEW" />
138-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
138-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
139            </intent-filter>
140        </activity>
141
142        <service
142-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
143            android:name="com.onesignal.FCMIntentService"
143-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
144            android:exported="false" />
144-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
145        <service
145-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
146            android:name="com.onesignal.FCMIntentJobService"
146-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
147            android:exported="false"
147-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
148            android:permission="android.permission.BIND_JOB_SERVICE" />
148-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
149        <service
149-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
150            android:name="com.onesignal.SyncService"
150-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
151            android:exported="false"
151-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
152            android:stopWithTask="true" />
152-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
153        <service
153-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
154            android:name="com.onesignal.SyncJobService"
154-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
155            android:exported="false"
155-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
156            android:permission="android.permission.BIND_JOB_SERVICE" />
156-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
157
158        <activity
158-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
159            android:name="com.onesignal.PermissionsActivity"
159-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
160            android:exported="false"
160-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
161            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
161-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
162
163        <receiver
163-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
164            android:name="com.onesignal.NotificationDismissReceiver"
164-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
165            android:exported="true" />
165-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
166        <receiver
166-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
167            android:name="com.onesignal.BootUpReceiver"
167-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
168            android:exported="true" >
168-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
169            <intent-filter>
169-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
170                <action android:name="android.intent.action.BOOT_COMPLETED" />
170-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
170-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
171                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
171-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
171-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
172            </intent-filter>
173        </receiver>
174        <receiver
174-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
175            android:name="com.onesignal.UpgradeReceiver"
175-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
176            android:exported="true" >
176-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
177            <intent-filter>
177-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
178                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
178-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
178-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
179            </intent-filter>
180        </receiver>
181
182        <activity
182-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
183            android:name="com.onesignal.NotificationOpenedReceiver"
183-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
184            android:excludeFromRecents="true"
184-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
185            android:exported="true"
185-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
186            android:noHistory="true"
186-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
187            android:taskAffinity=""
187-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
188            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
188-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
189        <activity
189-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
190            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
190-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
191            android:excludeFromRecents="true"
191-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
192            android:exported="true"
192-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
193            android:noHistory="true"
193-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
194-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
195
196        <provider
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
197            android:name="androidx.startup.InitializationProvider"
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
198            android:authorities="com.Zumbla.Burst2025.androidx-startup"
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
199            android:exported="false" >
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
200            <meta-data
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.emoji2.text.EmojiCompatInitializer"
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
202                android:value="androidx.startup" />
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
204                android:name="androidx.work.WorkManagerInitializer"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
205                android:value="androidx.startup" />
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
206            <meta-data
206-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
207-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
208                android:value="androidx.startup" />
208-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
209            <meta-data
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
211                android:value="androidx.startup" />
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
212        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
213        <activity
213-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:53:9-58:43
214            android:name="com.google.android.gms.ads.AdActivity"
214-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:54:13-65
215            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
215-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:55:13-122
216            android:exported="false"
216-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:56:13-37
217            android:theme="@android:style/Theme.Translucent" />
217-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:57:13-61
218
219        <provider
219-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:60:9-65:43
220            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
220-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:61:13-76
221            android:authorities="com.Zumbla.Burst2025.mobileadsinitprovider"
221-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:62:13-73
222            android:exported="false"
222-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:63:13-37
223            android:initOrder="100" />
223-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:64:13-36
224
225        <service
225-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:67:9-71:43
226            android:name="com.google.android.gms.ads.AdService"
226-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:68:13-64
227            android:enabled="true"
227-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:69:13-35
228            android:exported="false" />
228-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:70:13-37
229
230        <activity
230-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:73:9-77:43
231            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
231-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:74:13-82
232            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
232-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:75:13-122
233            android:exported="false" />
233-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:76:13-37
234
235        <receiver
235-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
236            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
236-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
237            android:enabled="true"
237-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
238            android:exported="false" >
238-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
239        </receiver>
240
241        <service
241-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
242            android:name="com.google.android.gms.measurement.AppMeasurementService"
242-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
243            android:enabled="true"
243-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
244            android:exported="false" />
244-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
245        <service
245-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
246            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
246-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
247            android:enabled="true"
247-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
248            android:exported="false"
248-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
249            android:permission="android.permission.BIND_JOB_SERVICE" />
249-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
250        <service
250-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
251            android:name="com.google.firebase.components.ComponentDiscoveryService"
251-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
252            android:directBootAware="true"
252-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
253            android:exported="false" >
253-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
254            <meta-data
254-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
255                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
255-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
257            <meta-data
257-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
258                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
258-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
260            <meta-data
260-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
261                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
261-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
263            <meta-data
263-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
264                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
264-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
266            <meta-data
266-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
267                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
267-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
268                android:value="com.google.firebase.components.ComponentRegistrar" />
268-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
269            <meta-data
269-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
270                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
270-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
271                android:value="com.google.firebase.components.ComponentRegistrar" />
271-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
272            <meta-data
272-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
273                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
274                android:value="com.google.firebase.components.ComponentRegistrar" />
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
275            <meta-data
275-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
276                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
276-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
278        </service>
279
280        <receiver
280-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
281            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
281-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
282            android:exported="true"
282-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
283            android:permission="com.google.android.c2dm.permission.SEND" >
283-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
284            <intent-filter>
284-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
285                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
285-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
285-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
286            </intent-filter>
287
288            <meta-data
288-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
289                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
289-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
290                android:value="true" />
290-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
291        </receiver>
292        <!--
293             FirebaseMessagingService performs security checks at runtime,
294             but set to not exported to explicitly avoid allowing another app to call it.
295        -->
296        <service
296-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
297            android:name="com.google.firebase.messaging.FirebaseMessagingService"
297-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
298            android:directBootAware="true"
298-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
299            android:exported="false" >
299-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
300            <intent-filter android:priority="-500" >
300-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
300-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
301                <action android:name="com.google.firebase.MESSAGING_EVENT" />
301-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:17-78
301-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:25-75
302            </intent-filter>
303        </service>
304
305        <provider
305-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
306            android:name="com.google.firebase.provider.FirebaseInitProvider"
306-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
307            android:authorities="com.Zumbla.Burst2025.firebaseinitprovider"
307-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
308            android:directBootAware="true"
308-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
309            android:exported="false"
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
310            android:initOrder="100" />
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
311
312        <activity
312-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
313            android:name="com.google.android.gms.common.api.GoogleApiActivity"
313-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
314            android:exported="false"
314-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
315            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
315-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
316
317        <uses-library
317-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
318            android:name="android.ext.adservices"
318-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
319            android:required="false" />
319-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
320
321        <service
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
322            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
324            android:enabled="@bool/enable_system_alarm_service_default"
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
325            android:exported="false" />
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
326        <service
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
327            android:name="androidx.work.impl.background.systemjob.SystemJobService"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
329            android:enabled="@bool/enable_system_job_service_default"
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
330            android:exported="true"
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
331            android:permission="android.permission.BIND_JOB_SERVICE" />
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
332        <service
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
333            android:name="androidx.work.impl.foreground.SystemForegroundService"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
335            android:enabled="@bool/enable_system_foreground_service_default"
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
336            android:exported="false" />
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
337
338        <receiver
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
339            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
341            android:enabled="true"
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
342            android:exported="false" />
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
343        <receiver
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
344            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
346            android:enabled="false"
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
347            android:exported="false" >
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
348            <intent-filter>
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
349                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
350                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
351            </intent-filter>
352        </receiver>
353        <receiver
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
354            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
355            android:directBootAware="false"
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
356            android:enabled="false"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
357            android:exported="false" >
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
358            <intent-filter>
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
359                <action android:name="android.intent.action.BATTERY_OKAY" />
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
360                <action android:name="android.intent.action.BATTERY_LOW" />
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
361            </intent-filter>
362        </receiver>
363        <receiver
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
364            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
365            android:directBootAware="false"
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
366            android:enabled="false"
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
367            android:exported="false" >
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
368            <intent-filter>
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
369                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
370                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
371            </intent-filter>
372        </receiver>
373        <receiver
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
374            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
375            android:directBootAware="false"
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
376            android:enabled="false"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
377            android:exported="false" >
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
378            <intent-filter>
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
379                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
383            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
385            android:enabled="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
388                <action android:name="android.intent.action.BOOT_COMPLETED" />
388-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
388-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
389                <action android:name="android.intent.action.TIME_SET" />
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
390                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
391            </intent-filter>
392        </receiver>
393        <receiver
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
394            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
396            android:enabled="@bool/enable_system_alarm_service_default"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
397            android:exported="false" >
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
398            <intent-filter>
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
399                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
400            </intent-filter>
401        </receiver>
402        <receiver
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
403            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
405            android:enabled="true"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
406            android:exported="true"
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
407            android:permission="android.permission.DUMP" >
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
408            <intent-filter>
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
409                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
410            </intent-filter>
411        </receiver>
412
413        <uses-library
413-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
414            android:name="androidx.window.extensions"
414-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
415            android:required="false" />
415-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
416        <uses-library
416-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
417            android:name="androidx.window.sidecar"
417-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
418            android:required="false" />
418-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
419
420        <meta-data
420-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
421            android:name="com.google.android.gms.version"
421-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
422            android:value="@integer/google_play_services_version" />
422-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
423
424        <receiver
424-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
425            android:name="androidx.profileinstaller.ProfileInstallReceiver"
425-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
426            android:directBootAware="false"
426-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
427            android:enabled="true"
427-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
428            android:exported="true"
428-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
429            android:permission="android.permission.DUMP" >
429-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
431                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
432            </intent-filter>
433            <intent-filter>
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
434                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
435            </intent-filter>
436            <intent-filter>
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
437                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
438            </intent-filter>
439            <intent-filter>
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
440                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
441            </intent-filter>
442        </receiver>
443
444        <service
444-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
445            android:name="androidx.room.MultiInstanceInvalidationService"
445-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
446            android:directBootAware="true"
446-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
447            android:exported="false" />
447-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
448        <service
448-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
449            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
449-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
450            android:exported="false" >
450-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
451            <meta-data
451-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
452                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
452-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
453                android:value="cct" />
453-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
454        </service>
455        <service
455-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
456            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
456-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
457            android:exported="false"
457-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
458            android:permission="android.permission.BIND_JOB_SERVICE" >
458-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
459        </service>
460
461        <receiver
461-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
462            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
462-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
463            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
463-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
464        <activity
464-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
465            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
465-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
466            android:exported="false"
466-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
467            android:stateNotNeeded="true"
467-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
468            android:theme="@style/Theme.PlayCore.Transparent" />
468-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
469    </application>
470
471</manifest>
