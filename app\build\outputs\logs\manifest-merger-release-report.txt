-- Merging decision tree log ---
manifest
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:2:1-45:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07d0e2c91df73b2c5aa60b52e6ec87b\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bb16bb95ce2c158213264f7a72032a8\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ce5be2cb47842cb9d09b8245a76377e\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3df4af221a288d9bcf50e45d897280\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:2:1-136:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9ac91b2ec0caf8f223af6026edbad61\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66b37f3f60883449bfc7382cd0423a97\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a96715ba9df777a8e995ff71e6bff2\transformed\jetified-play-services-ads-22.1.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b92185cac35614b8b4165ee7db94d60c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09741b2a618a5b5be4b1d1113c816472\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c6dc7f868ae98a3905656edc047122\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\246c7095695424e5b71bbeb13e90bb62\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:17:1-80:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d8829b7bc13ecdc7772c0705bb4b144\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\113dbc6fadfae5be27d056b39ec70667\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.play:review:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23b370df2b949db187b77939260e093b\transformed\jetified-review-2.0.2\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.gms:play-services-ads-base:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aed716b1d495d6cb95ff1d1dea8731c7\transformed\jetified-play-services-ads-base-22.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa87cd413a0d652126e22fdf99c6935a\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b12fc80029502247ea0b2bbfc0eeadf3\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a974b553f591dea012559bb8fa82b117\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da56ffeb35f370164e218af55bc9d3a8\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d14873a2294d75b7f328118064530156\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7d50ef5617ecfdd8224f129a1ea9d3\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd71c27ad61410b10e9c1a0740c800a0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5133056e5246005dcaa58c2c83824628\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c20bf3fd4997d1aa53546ade18130d7f\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a4a59b1564334936fa87aec53b2bf7f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b0c9ff9f73119acfb789a34c2fc14f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea5b4faa8d6938035ac03c21212c4350\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc248905bcb822a98a4917f53925dce\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f406da5904a9feec7df643223d2621\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7191ccde6098047016388f6525ccb577\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec4f821f85450c4771e84550280d52af\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea2b33775fd039a6de0c9f5f2bf1d1c2\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16d819b16df890cfd7e557cc61bd794d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\604466f0eeddd43f86869c1800271a2d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f573bbfd264a2031596bb3c5f4ab7c5\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084efabfc8ff2d1ba64bc52b32238993\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26aecba652def1ec775a3d65dcb30eb0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1efc03052e8ed1265be045f31d1a69b9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7fed520105eb42e01e31184f2e8825\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b15bf9680423266d7fe7f5665add40\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb3e08fe872a8e26da4c21babae46dc\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d43ebc733ddc38c532491626aadf1f0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80def792dcf53631452aa400603b741c\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1cca28f5e023272d0543ecf20f2cc56\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9514ae4a0c51323dabf5798744024b7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1906822bc62d4e244b9e425d62fa25e8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d877f177f0095ccd53a730dc3bfae13\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9094842c3ca9f7ac093e4fd08de69687\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9dd55976dd5ae39f237e6587c02b703\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63e296e8a0aaa5ba9920f7bf2db707c\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ac83015f4ae4b9e2b68db680985391\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9013936fdf66e1a28286508567a63464\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67310042735f2aad517ab1c0d3091659\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d29424da896feb40ec5ca2200bb0815e\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba46fa774244f1526b6100629c39781d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cfbd1879a951d6cdc0b5658a9b0f24d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f199e0e01cbeeecb5c2a0812c3e23e23\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\268c8b572666d04794a776a2d4796aee\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df410720c402a07b4343a03136270a32\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2b9f8fafbb00c42090f352b78ccd1b3\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d85927995953586d72d5cafaf32a27be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eadfff678cba8d55e531e22786834bc5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b98622a46a8feda97e28e7d49ca68445\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fafb5fb08294497a3184b8acc682bca6\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\132c9a327692f01ac0d7aa7dc76d222e\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5901d3ddeeeab79e4b46f7369feeefdf\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54e78eb528e07f1c6275f660360b3d00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db19d08491cd6424975a75465949d19d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c98cd9860e968f091d258381e9b4b401\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0918ad5dcc27981f7c9281a4be1692f\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06b228c271488a425db28f397e43ca03\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2fd2894156f3e6448680b898b76e84\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:2:1-21:12
	package
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:4:5-35
		INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:17:5-67
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:17:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:5-79
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:8:5-68
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:5-68
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:8:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:9:5-82
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:5-82
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:9:22-79
uses-permission#android.permission.VIBRATE
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:10:5-66
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:5-66
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:5-66
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:10:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:11:5-81
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:5-81
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:5-81
REJECTED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:27:5-29:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:12:5-77
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:16:5-77
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:16:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:12:22-74
application
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:14:5-44:19
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:14:5-44:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07d0e2c91df73b2c5aa60b52e6ec87b\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07d0e2c91df73b2c5aa60b52e6ec87b\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bb16bb95ce2c158213264f7a72032a8\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bb16bb95ce2c158213264f7a72032a8\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ce5be2cb47842cb9d09b8245a76377e\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ce5be2cb47842cb9d09b8245a76377e\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:49:5-134:19
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:49:5-134:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:50:5-78:19
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:50:5-78:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\113dbc6fadfae5be27d056b39ec70667\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\113dbc6fadfae5be27d056b39ec70667\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:review:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23b370df2b949db187b77939260e093b\transformed\jetified-review-2.0.2\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:review:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23b370df2b949db187b77939260e093b\transformed\jetified-review-2.0.2\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.gms:play-services-ads-base:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aed716b1d495d6cb95ff1d1dea8731c7\transformed\jetified-play-services-ads-base-22.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aed716b1d495d6cb95ff1d1dea8731c7\transformed\jetified-play-services-ads-base-22.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa87cd413a0d652126e22fdf99c6935a\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa87cd413a0d652126e22fdf99c6935a\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b12fc80029502247ea0b2bbfc0eeadf3\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b12fc80029502247ea0b2bbfc0eeadf3\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a974b553f591dea012559bb8fa82b117\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a974b553f591dea012559bb8fa82b117\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b0c9ff9f73119acfb789a34c2fc14f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b0c9ff9f73119acfb789a34c2fc14f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b15bf9680423266d7fe7f5665add40\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b15bf9680423266d7fe7f5665add40\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb3e08fe872a8e26da4c21babae46dc\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb3e08fe872a8e26da4c21babae46dc\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d43ebc733ddc38c532491626aadf1f0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d43ebc733ddc38c532491626aadf1f0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d85927995953586d72d5cafaf32a27be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d85927995953586d72d5cafaf32a27be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:18:9-41
	android:hardwareAccelerated
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:16:9-43
	tools:targetApi
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:22:9-28
	android:icon
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:15:9-35
	android:theme
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:21:9-42
	android:usesCleartextTraffic
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:20:9-44
meta-data#main_id
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:9-64
	android:value
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:43-61
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:20-42
activity#com.Zumbla.Burst2025.MainActivity
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:24:9-32:20
	android:exported
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:25:13-36
	android:configChanges
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:27:13-59
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:26:13-61
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:30:27-74
activity#com.Zumbla.Burst2025.PrivacyActivity
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:33:9-72
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:33:19-70
meta-data#com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:35:9-37:35
	android:value
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:37:13-33
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:36:13-81
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:38:9-40:70
	android:value
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:40:13-67
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:39:13-69
meta-data#com.onesignal.NotificationOpened.DEFAULT
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:9-102
	android:value
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:76-99
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:20-75
meta-data#com.onesignal.NotificationWillShowInForeground.DEFAULT
ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:9-115
	android:value
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:90-112
	android:name
		ADDED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:20-89
uses-sdk
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07d0e2c91df73b2c5aa60b52e6ec87b\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07d0e2c91df73b2c5aa60b52e6ec87b\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bb16bb95ce2c158213264f7a72032a8\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bb16bb95ce2c158213264f7a72032a8\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ce5be2cb47842cb9d09b8245a76377e\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ce5be2cb47842cb9d09b8245a76377e\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3df4af221a288d9bcf50e45d897280\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3df4af221a288d9bcf50e45d897280\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:4:5-44
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:4:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9ac91b2ec0caf8f223af6026edbad61\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9ac91b2ec0caf8f223af6026edbad61\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66b37f3f60883449bfc7382cd0423a97\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66b37f3f60883449bfc7382cd0423a97\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a96715ba9df777a8e995ff71e6bff2\transformed\jetified-play-services-ads-22.1.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a96715ba9df777a8e995ff71e6bff2\transformed\jetified-play-services-ads-22.1.0\AndroidManifest.xml:21:5-23:52
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b92185cac35614b8b4165ee7db94d60c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b92185cac35614b8b4165ee7db94d60c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09741b2a618a5b5be4b1d1113c816472\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09741b2a618a5b5be4b1d1113c816472\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c6dc7f868ae98a3905656edc047122\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c6dc7f868ae98a3905656edc047122\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\246c7095695424e5b71bbeb13e90bb62\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\246c7095695424e5b71bbeb13e90bb62\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d8829b7bc13ecdc7772c0705bb4b144\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d8829b7bc13ecdc7772c0705bb4b144\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\113dbc6fadfae5be27d056b39ec70667\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\113dbc6fadfae5be27d056b39ec70667\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:review:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23b370df2b949db187b77939260e093b\transformed\jetified-review-2.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:review:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23b370df2b949db187b77939260e093b\transformed\jetified-review-2.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.gms:play-services-ads-base:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aed716b1d495d6cb95ff1d1dea8731c7\transformed\jetified-play-services-ads-base-22.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aed716b1d495d6cb95ff1d1dea8731c7\transformed\jetified-play-services-ads-base-22.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa87cd413a0d652126e22fdf99c6935a\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa87cd413a0d652126e22fdf99c6935a\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b12fc80029502247ea0b2bbfc0eeadf3\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b12fc80029502247ea0b2bbfc0eeadf3\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a974b553f591dea012559bb8fa82b117\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a974b553f591dea012559bb8fa82b117\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c20e127abcc95d26aac731bf1ce3a6\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da56ffeb35f370164e218af55bc9d3a8\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da56ffeb35f370164e218af55bc9d3a8\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d14873a2294d75b7f328118064530156\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d14873a2294d75b7f328118064530156\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7d50ef5617ecfdd8224f129a1ea9d3\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7d50ef5617ecfdd8224f129a1ea9d3\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd71c27ad61410b10e9c1a0740c800a0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd71c27ad61410b10e9c1a0740c800a0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5133056e5246005dcaa58c2c83824628\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5133056e5246005dcaa58c2c83824628\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c20bf3fd4997d1aa53546ade18130d7f\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c20bf3fd4997d1aa53546ade18130d7f\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a4a59b1564334936fa87aec53b2bf7f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a4a59b1564334936fa87aec53b2bf7f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b0c9ff9f73119acfb789a34c2fc14f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b0c9ff9f73119acfb789a34c2fc14f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea5b4faa8d6938035ac03c21212c4350\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea5b4faa8d6938035ac03c21212c4350\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc248905bcb822a98a4917f53925dce\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc248905bcb822a98a4917f53925dce\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f406da5904a9feec7df643223d2621\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f406da5904a9feec7df643223d2621\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7191ccde6098047016388f6525ccb577\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7191ccde6098047016388f6525ccb577\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec4f821f85450c4771e84550280d52af\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec4f821f85450c4771e84550280d52af\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea2b33775fd039a6de0c9f5f2bf1d1c2\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea2b33775fd039a6de0c9f5f2bf1d1c2\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16d819b16df890cfd7e557cc61bd794d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16d819b16df890cfd7e557cc61bd794d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\604466f0eeddd43f86869c1800271a2d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\604466f0eeddd43f86869c1800271a2d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f573bbfd264a2031596bb3c5f4ab7c5\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f573bbfd264a2031596bb3c5f4ab7c5\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084efabfc8ff2d1ba64bc52b32238993\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084efabfc8ff2d1ba64bc52b32238993\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26aecba652def1ec775a3d65dcb30eb0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26aecba652def1ec775a3d65dcb30eb0\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1efc03052e8ed1265be045f31d1a69b9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1efc03052e8ed1265be045f31d1a69b9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7fed520105eb42e01e31184f2e8825\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7fed520105eb42e01e31184f2e8825\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b15bf9680423266d7fe7f5665add40\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b15bf9680423266d7fe7f5665add40\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb3e08fe872a8e26da4c21babae46dc\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb3e08fe872a8e26da4c21babae46dc\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d43ebc733ddc38c532491626aadf1f0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d43ebc733ddc38c532491626aadf1f0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80def792dcf53631452aa400603b741c\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80def792dcf53631452aa400603b741c\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1cca28f5e023272d0543ecf20f2cc56\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1cca28f5e023272d0543ecf20f2cc56\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9514ae4a0c51323dabf5798744024b7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9514ae4a0c51323dabf5798744024b7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1906822bc62d4e244b9e425d62fa25e8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1906822bc62d4e244b9e425d62fa25e8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d877f177f0095ccd53a730dc3bfae13\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d877f177f0095ccd53a730dc3bfae13\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9094842c3ca9f7ac093e4fd08de69687\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9094842c3ca9f7ac093e4fd08de69687\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9dd55976dd5ae39f237e6587c02b703\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9dd55976dd5ae39f237e6587c02b703\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63e296e8a0aaa5ba9920f7bf2db707c\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63e296e8a0aaa5ba9920f7bf2db707c\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ac83015f4ae4b9e2b68db680985391\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ac83015f4ae4b9e2b68db680985391\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9013936fdf66e1a28286508567a63464\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9013936fdf66e1a28286508567a63464\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67310042735f2aad517ab1c0d3091659\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67310042735f2aad517ab1c0d3091659\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d29424da896feb40ec5ca2200bb0815e\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d29424da896feb40ec5ca2200bb0815e\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba46fa774244f1526b6100629c39781d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba46fa774244f1526b6100629c39781d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cfbd1879a951d6cdc0b5658a9b0f24d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cfbd1879a951d6cdc0b5658a9b0f24d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f199e0e01cbeeecb5c2a0812c3e23e23\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f199e0e01cbeeecb5c2a0812c3e23e23\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\268c8b572666d04794a776a2d4796aee\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\268c8b572666d04794a776a2d4796aee\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df410720c402a07b4343a03136270a32\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df410720c402a07b4343a03136270a32\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2b9f8fafbb00c42090f352b78ccd1b3\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2b9f8fafbb00c42090f352b78ccd1b3\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d85927995953586d72d5cafaf32a27be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d85927995953586d72d5cafaf32a27be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eadfff678cba8d55e531e22786834bc5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eadfff678cba8d55e531e22786834bc5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b98622a46a8feda97e28e7d49ca68445\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b98622a46a8feda97e28e7d49ca68445\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fafb5fb08294497a3184b8acc682bca6\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fafb5fb08294497a3184b8acc682bca6\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\132c9a327692f01ac0d7aa7dc76d222e\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\132c9a327692f01ac0d7aa7dc76d222e\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5901d3ddeeeab79e4b46f7369feeefdf\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5901d3ddeeeab79e4b46f7369feeefdf\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54e78eb528e07f1c6275f660360b3d00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54e78eb528e07f1c6275f660360b3d00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db19d08491cd6424975a75465949d19d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db19d08491cd6424975a75465949d19d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c98cd9860e968f091d258381e9b4b401\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c98cd9860e968f091d258381e9b4b401\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0918ad5dcc27981f7c9281a4be1692f\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0918ad5dcc27981f7c9281a4be1692f\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06b228c271488a425db28f397e43ca03\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06b228c271488a425db28f397e43ca03\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2fd2894156f3e6448680b898b76e84\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2fd2894156f3e6448680b898b76e84\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a96715ba9df777a8e995ff71e6bff2\transformed\jetified-play-services-ads-22.1.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml
permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
	android:protectionLevel
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
permission#com.Zumbla.Burst2025.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
	android:protectionLevel
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
uses-permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
uses-permission#com.Zumbla.Burst2025.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
receiver#com.onesignal.FCMBroadcastReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:${applicationId}
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
	android:priority
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:com.Zumbla.Burst2025
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
	android:priority
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
category#${applicationId}
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
category#com.Zumbla.Burst2025
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
service#com.onesignal.HmsMessageServiceOneSignal
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
intent-filter#action:name:com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
action#com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
activity#com.onesignal.NotificationOpenedActivityHMS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
intent-filter#action:name:android.intent.action.VIEW
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
action#android.intent.action.VIEW
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
service#com.onesignal.FCMIntentService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
service#com.onesignal.FCMIntentJobService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
service#com.onesignal.SyncService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
	android:stopWithTask
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
service#com.onesignal.SyncJobService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
activity#com.onesignal.PermissionsActivity
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
receiver#com.onesignal.NotificationDismissReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
receiver#com.onesignal.BootUpReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
receiver#com.onesignal.UpgradeReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
activity#com.onesignal.NotificationOpenedReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
	android:excludeFromRecents
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
	android:taskAffinity
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
activity#com.onesignal.NotificationOpenedReceiverAndroid22AndOlder
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
	android:excludeFromRecents
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69a7759cd1a45cc4bb6a3bd6e87db5ab\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e8b668a27b7c94b6a069554e927b0b0\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:26:22-76
queries
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:32:5-48:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:35:9-41:18
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:38:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:38:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:40:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:40:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:44:9-46:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:45:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:45:21-87
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:53:9-58:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:56:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:58:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:55:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:57:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:54:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:60:9-65:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:62:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:63:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:65:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:64:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:61:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:67:9-71:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:69:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:70:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:71:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:68:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:73:9-77:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:77:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:75:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:74:13-82
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cca78e5b56ee645199d217c087747a0f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fff8db8b445b0453e7f02bd7f2cb2b4\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:17-78
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:25-75
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.Zumbla.Burst2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.Zumbla.Burst2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
