1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Zumbla.Burst2025"
4    android:versionCode="3"
5    android:versionName="2.1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:6:5-67
11-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:7:5-79
12-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:7:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:8:5-68
13-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:8:22-65
14    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
14-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:9:5-82
14-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:9:22-79
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:10:5-66
15-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:10:22-63
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:11:5-81
16-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:11:22-78
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:12:5-77
17-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:12:22-74
18    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
19    <permission
19-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
20        android:name="com.Zumbla.Burst2025.permission.C2D_MESSAGE"
20-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
21        android:protectionLevel="signature" />
21-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
22
23    <uses-permission android:name="com.Zumbla.Burst2025.permission.C2D_MESSAGE" /> <!-- START: ShortcutBadger -->
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
24    <!-- Samsung -->
25    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
25-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
25-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
26    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
26-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
27    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
27-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
28    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
28-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
29    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
29-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
29-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
30    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
30-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
30-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
31    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
31-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
31-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
32    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
32-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
32-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
33    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
33-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
34    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
34-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
35    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
35-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
35-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
36    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
36-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
36-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
37    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
37-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
37-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
38    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
38-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
38-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
39    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
40    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
40-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
40-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
41    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Android package visibility setting -->
41-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:26:5-79
41-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:26:22-76
42    <queries>
42-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:32:5-48:15
43
44        <!-- For browser content -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:35:9-41:18
46            <action android:name="android.intent.action.VIEW" />
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
47
48            <category android:name="android.intent.category.BROWSABLE" />
48-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:38:13-74
48-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:38:23-71
49
50            <data android:scheme="https" />
50-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:40:13-44
50-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:40:19-41
51        </intent>
52        <!-- End of browser content -->
53        <!-- For CustomTabsService -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:44:9-46:18
55            <action android:name="android.support.customtabs.action.CustomTabsService" />
55-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:45:13-90
55-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:45:21-87
56        </intent>
57        <!-- End of CustomTabsService -->
58    </queries>
59
60    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
60-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
60-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
61    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
61-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
61-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
62    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
62-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
62-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
63    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
63-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
63-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
64
65    <permission
65-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
66        android:name="com.Zumbla.Burst2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
66-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
67        android:protectionLevel="signature" />
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
68
69    <uses-permission android:name="com.Zumbla.Burst2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
70
71    <application
71-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:14:5-44:19
72        android:allowBackup="true"
72-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:15:9-35
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3930e81a22611b2ddd91d6283578bd0f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:hardwareAccelerated="true"
76-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:16:9-43
77        android:icon="@mipmap/ic_launcher"
77-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:17:9-43
78        android:label="@string/app_name"
78-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:18:9-41
79        android:supportsRtl="true"
79-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:19:9-35
80        android:testOnly="true"
81        android:theme="@style/Theme.Game"
81-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:21:9-42
82        android:usesCleartextTraffic="true" >
82-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:20:9-44
83        <meta-data
83-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:9-64
84            android:name="main_id"
84-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:20-42
85            android:value="91" />
85-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:23:43-61
86
87        <activity
87-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:24:9-32:20
88            android:name="com.Zumbla.Burst2025.MainActivity"
88-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:26:13-61
89            android:configChanges="orientation|screenSize"
89-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:27:13-59
90            android:exported="true" >
90-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:25:13-36
91            <intent-filter>
91-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:28:13-31:29
92                <action android:name="android.intent.action.MAIN" />
92-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:29:17-69
92-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:29:25-66
93
94                <category android:name="android.intent.category.LAUNCHER" />
94-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:30:17-77
94-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:30:27-74
95            </intent-filter>
96        </activity>
97        <activity android:name="com.Zumbla.Burst2025.PrivacyActivity" />
97-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:33:9-72
97-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:33:19-70
98
99        <meta-data
99-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:35:9-37:35
100            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
100-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:36:13-81
101            android:value="true" />
101-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:37:13-33
102        <meta-data
102-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:38:9-40:70
103            android:name="com.google.android.gms.ads.APPLICATION_ID"
103-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:39:13-69
104            android:value="ca-app-pub-7841751633097845~7145671857" />
104-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:40:13-67
105        <!-- OneSignal meta-data -->
106        <meta-data
106-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:9-102
107            android:name="com.onesignal.NotificationOpened.DEFAULT"
107-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:20-75
108            android:value="DISABLE" />
108-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:42:76-99
109        <meta-data
109-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:9-115
110            android:name="com.onesignal.NotificationWillShowInForeground.DEFAULT"
110-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:20-89
111            android:value="ENABLE" />
111-->Z:\Zumbla Burst3\app\src\main\AndroidManifest.xml:43:90-112
112
113        <receiver
113-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
114            android:name="com.onesignal.FCMBroadcastReceiver"
114-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
115            android:exported="true"
115-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
116            android:permission="com.google.android.c2dm.permission.SEND" >
116-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
117
118            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
119            <intent-filter android:priority="999" >
119-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
119-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
120                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
120-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
120-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
121
122                <category android:name="com.Zumbla.Burst2025" />
122-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
122-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
123            </intent-filter>
124        </receiver>
125
126        <service
126-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
127            android:name="com.onesignal.HmsMessageServiceOneSignal"
127-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
128            android:exported="false" >
128-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
129            <intent-filter>
129-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
130                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
130-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
130-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
131            </intent-filter>
132        </service>
133
134        <activity
134-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
135            android:name="com.onesignal.NotificationOpenedActivityHMS"
135-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
136            android:exported="true"
136-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
137            android:noHistory="true"
137-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
139            <intent-filter>
139-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
140                <action android:name="android.intent.action.VIEW" />
140-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
140-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
141            </intent-filter>
142        </activity>
143
144        <service
144-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
145            android:name="com.onesignal.FCMIntentService"
145-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
146            android:exported="false" />
146-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
147        <service
147-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
148            android:name="com.onesignal.FCMIntentJobService"
148-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
149            android:exported="false"
149-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
150            android:permission="android.permission.BIND_JOB_SERVICE" />
150-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
151        <service
151-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
152            android:name="com.onesignal.SyncService"
152-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
153            android:exported="false"
153-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
154            android:stopWithTask="true" />
154-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
155        <service
155-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
156            android:name="com.onesignal.SyncJobService"
156-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
157            android:exported="false"
157-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
158            android:permission="android.permission.BIND_JOB_SERVICE" />
158-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
159
160        <activity
160-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
161            android:name="com.onesignal.PermissionsActivity"
161-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
162            android:exported="false"
162-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
163            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
163-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
164
165        <receiver
165-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
166            android:name="com.onesignal.NotificationDismissReceiver"
166-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
167            android:exported="true" />
167-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
168        <receiver
168-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
169            android:name="com.onesignal.BootUpReceiver"
169-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
170            android:exported="true" >
170-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
171            <intent-filter>
171-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
172                <action android:name="android.intent.action.BOOT_COMPLETED" />
172-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
172-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
173                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
173-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
173-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
177            android:name="com.onesignal.UpgradeReceiver"
177-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
178            android:exported="true" >
178-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
179            <intent-filter>
179-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
180                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
180-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
180-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
181            </intent-filter>
182        </receiver>
183
184        <activity
184-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
185            android:name="com.onesignal.NotificationOpenedReceiver"
185-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
186            android:excludeFromRecents="true"
186-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
187            android:exported="true"
187-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
188            android:noHistory="true"
188-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
189            android:taskAffinity=""
189-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
190-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
191        <activity
191-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
192            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
192-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
193            android:excludeFromRecents="true"
193-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
194            android:exported="true"
194-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
195            android:noHistory="true"
195-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
196            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
196-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
197
198        <provider
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
199            android:name="androidx.startup.InitializationProvider"
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
200            android:authorities="com.Zumbla.Burst2025.androidx-startup"
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
201            android:exported="false" >
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
202            <meta-data
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
203                android:name="androidx.emoji2.text.EmojiCompatInitializer"
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
204                android:value="androidx.startup" />
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984411b5a4f47a68f9046c1959b8f787\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
205            <meta-data
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
206                android:name="androidx.work.WorkManagerInitializer"
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
207                android:value="androidx.startup" />
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
208            <meta-data
208-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
209                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
209-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
210                android:value="androidx.startup" />
210-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3cf6d2b6dd320a59efaf4c0e125a44\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
211            <meta-data
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
213                android:value="androidx.startup" />
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
214        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
215        <activity
215-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:53:9-58:43
216            android:name="com.google.android.gms.ads.AdActivity"
216-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:54:13-65
217            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
217-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:55:13-122
218            android:exported="false"
218-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:56:13-37
219            android:theme="@android:style/Theme.Translucent" />
219-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:57:13-61
220
221        <provider
221-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:60:9-65:43
222            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
222-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:61:13-76
223            android:authorities="com.Zumbla.Burst2025.mobileadsinitprovider"
223-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:62:13-73
224            android:exported="false"
224-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:63:13-37
225            android:initOrder="100" />
225-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:64:13-36
226
227        <service
227-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:67:9-71:43
228            android:name="com.google.android.gms.ads.AdService"
228-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:68:13-64
229            android:enabled="true"
229-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:69:13-35
230            android:exported="false" />
230-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:70:13-37
231
232        <activity
232-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:73:9-77:43
233            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
233-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:74:13-82
234            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
234-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:75:13-122
235            android:exported="false" />
235-->[com.google.android.gms:play-services-ads-lite:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b82613446b37e75b334358ddb568a1c\transformed\jetified-play-services-ads-lite-22.1.0\AndroidManifest.xml:76:13-37
236
237        <receiver
237-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
238            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
238-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
239            android:enabled="true"
239-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
240            android:exported="false" >
240-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
241        </receiver>
242
243        <service
243-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
244            android:name="com.google.android.gms.measurement.AppMeasurementService"
244-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
245            android:enabled="true"
245-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
246            android:exported="false" />
246-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
247        <service
247-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
248            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
248-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
249            android:enabled="true"
249-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
250            android:exported="false"
250-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
251            android:permission="android.permission.BIND_JOB_SERVICE" />
251-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eace7eb892ac633afa71ddcde7252cb7\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
252        <service
252-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
253            android:name="com.google.firebase.components.ComponentDiscoveryService"
253-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
254            android:directBootAware="true"
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
255            android:exported="false" >
255-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
256            <meta-data
256-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
257                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
257-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d7fdba4b40a441f903fecbd0019d614\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
259            <meta-data
259-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
260                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
260-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
262            <meta-data
262-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
263                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
263-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
265            <meta-data
265-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
266                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
266-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
267                android:value="com.google.firebase.components.ComponentRegistrar" />
267-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
268            <meta-data
268-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
269                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
269-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
270                android:value="com.google.firebase.components.ComponentRegistrar" />
270-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53f586e34856cdf76d3e73eeccaea639\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
271            <meta-data
271-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
272                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
272-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
273                android:value="com.google.firebase.components.ComponentRegistrar" />
273-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf69d619c610af66d141ee88c6912663\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
274            <meta-data
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
275                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
275-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
277            <meta-data
277-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
278                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
278-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d626f7ab7b2353f866e5655302592c88\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
280        </service>
281
282        <receiver
282-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
283            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
283-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
284            android:exported="true"
284-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
285            android:permission="com.google.android.c2dm.permission.SEND" >
285-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
286            <intent-filter>
286-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
287                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
287-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
287-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
288            </intent-filter>
289
290            <meta-data
290-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
291                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
291-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
292                android:value="true" />
292-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
293        </receiver>
294        <!--
295             FirebaseMessagingService performs security checks at runtime,
296             but set to not exported to explicitly avoid allowing another app to call it.
297        -->
298        <service
298-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
299            android:name="com.google.firebase.messaging.FirebaseMessagingService"
299-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
300            android:directBootAware="true"
300-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
301            android:exported="false" >
301-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
302            <intent-filter android:priority="-500" >
302-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
302-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
303                <action android:name="com.google.firebase.MESSAGING_EVENT" />
303-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:17-78
303-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1768923426931bfd486ccbd696691372\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:25-75
304            </intent-filter>
305        </service>
306
307        <provider
307-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
308            android:name="com.google.firebase.provider.FirebaseInitProvider"
308-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
309            android:authorities="com.Zumbla.Burst2025.firebaseinitprovider"
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
310            android:directBootAware="true"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
311            android:exported="false"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
312            android:initOrder="100" />
312-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66ee8fe64aa0261716a1f7f72358b6aa\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
313
314        <activity
314-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
315            android:name="com.google.android.gms.common.api.GoogleApiActivity"
315-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
316            android:exported="false"
316-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
317            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
317-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fb92ec23f9579e12652e527e2b5ff5e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
318
319        <uses-library
319-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
320            android:name="android.ext.adservices"
320-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
321            android:required="false" />
321-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8d63d7946d4faf1dc5c877acbbe29df\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
322
323        <service
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
324            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
325            android:directBootAware="false"
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
326            android:enabled="@bool/enable_system_alarm_service_default"
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
327            android:exported="false" />
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
328        <service
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
329            android:name="androidx.work.impl.background.systemjob.SystemJobService"
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
330            android:directBootAware="false"
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
331            android:enabled="@bool/enable_system_job_service_default"
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
332            android:exported="true"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
333            android:permission="android.permission.BIND_JOB_SERVICE" />
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
334        <service
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
335            android:name="androidx.work.impl.foreground.SystemForegroundService"
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
336            android:directBootAware="false"
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
337            android:enabled="@bool/enable_system_foreground_service_default"
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
338            android:exported="false" />
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
339
340        <receiver
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
341            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
342            android:directBootAware="false"
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
343            android:enabled="true"
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
344            android:exported="false" />
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
345        <receiver
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
346            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
347            android:directBootAware="false"
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
348            android:enabled="false"
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
349            android:exported="false" >
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
350            <intent-filter>
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
351                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
352                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
353            </intent-filter>
354        </receiver>
355        <receiver
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
356            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
357            android:directBootAware="false"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
358            android:enabled="false"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
359            android:exported="false" >
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
360            <intent-filter>
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
361                <action android:name="android.intent.action.BATTERY_OKAY" />
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
362                <action android:name="android.intent.action.BATTERY_LOW" />
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
363            </intent-filter>
364        </receiver>
365        <receiver
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
366            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
367            android:directBootAware="false"
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
368            android:enabled="false"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
369            android:exported="false" >
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
370            <intent-filter>
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
371                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
372                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
373            </intent-filter>
374        </receiver>
375        <receiver
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
376            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
377            android:directBootAware="false"
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
378            android:enabled="false"
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
379            android:exported="false" >
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
380            <intent-filter>
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
381                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
382            </intent-filter>
383        </receiver>
384        <receiver
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
385            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
386            android:directBootAware="false"
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
387            android:enabled="false"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
388            android:exported="false" >
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
389            <intent-filter>
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
390                <action android:name="android.intent.action.BOOT_COMPLETED" />
390-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
390-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a50d7aef037c78b7a0a4713522ff9c\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
391                <action android:name="android.intent.action.TIME_SET" />
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
392                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
393            </intent-filter>
394        </receiver>
395        <receiver
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
396            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
397            android:directBootAware="false"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
398            android:enabled="@bool/enable_system_alarm_service_default"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
399            android:exported="false" >
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
400            <intent-filter>
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
401                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
402            </intent-filter>
403        </receiver>
404        <receiver
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
405            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
406            android:directBootAware="false"
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
407            android:enabled="true"
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
408            android:exported="true"
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
409            android:permission="android.permission.DUMP" >
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
410            <intent-filter>
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
411                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5553b05152722695a88ae519b45f0b1a\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
412            </intent-filter>
413        </receiver>
414
415        <uses-library
415-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
416            android:name="androidx.window.extensions"
416-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
417            android:required="false" />
417-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
418        <uses-library
418-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
419            android:name="androidx.window.sidecar"
419-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
420            android:required="false" />
420-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2bded5112b9f8559a7b64040417a465\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
421
422        <meta-data
422-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
423            android:name="com.google.android.gms.version"
423-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
424            android:value="@integer/google_play_services_version" />
424-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38366cef8399d337aed3da4a7ed40977\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
425
426        <receiver
426-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
427            android:name="androidx.profileinstaller.ProfileInstallReceiver"
427-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
428            android:directBootAware="false"
428-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
429            android:enabled="true"
429-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
430            android:exported="true"
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
431            android:permission="android.permission.DUMP" >
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
432            <intent-filter>
432-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
433                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
434            </intent-filter>
435            <intent-filter>
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
436                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
437            </intent-filter>
438            <intent-filter>
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
439                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
440            </intent-filter>
441            <intent-filter>
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
442                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc4b881eb7c69caf2ad70d0ff9f3faa6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
443            </intent-filter>
444        </receiver>
445
446        <service
446-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
447            android:name="androidx.room.MultiInstanceInvalidationService"
447-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
448            android:directBootAware="true"
448-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
449            android:exported="false" />
449-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bb2c7dce0662ea23e077f8433440493\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
450        <service
450-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
451            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
451-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
452            android:exported="false" >
452-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
453            <meta-data
453-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
454                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
454-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
455                android:value="cct" />
455-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e7aeed3516bde84c8d7f0a60e26cc2f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
456        </service>
457        <service
457-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
458            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
458-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
459            android:exported="false"
459-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
460            android:permission="android.permission.BIND_JOB_SERVICE" >
460-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
461        </service>
462
463        <receiver
463-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
464            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
464-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
465            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
465-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1056193e199f845b1112dcc88f4b31a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
466        <activity
466-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
467            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
467-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
468            android:exported="false"
468-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
469            android:stateNotNeeded="true"
469-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
470            android:theme="@style/Theme.PlayCore.Transparent" />
470-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f61d4bf9734bccb9472d14546e158b7\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
471    </application>
472
473</manifest>
